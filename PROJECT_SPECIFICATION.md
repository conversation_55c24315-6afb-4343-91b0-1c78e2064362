# VoiceForge - AI文字转语音SaaS平台

## 项目概述

VoiceForge是一个基于Google Gemini API的付费文字转语音(TTS)应用，使用T3技术栈构建。该平台提供高质量的AI语音生成服务，支持多种语言、语音风格和商业化功能。

## 技术栈

### 核心框架
- **Next.js 14** (App Router) - React全栈框架
- **TypeScript** - 类型安全的JavaScript
- **T3 Stack** - 现代化全栈开发工具链

### 数据层
- **Prisma** - 类型安全的ORM
- **PostgreSQL** - 主数据库
- **Redis** - 缓存和会话存储

### API层
- **tRPC** - 端到端类型安全的API
- **Google Gemini API** - AI语音生成服务
- **Stripe API** - 支付处理

### 认证与安全
- **NextAuth.js** - 身份认证
- **JWT** - 会话管理
- **bcrypt** - 密码加密

### 前端
- **Tailwind CSS** - 实用优先的CSS框架
- **Headless UI** - 无样式组件库
- **React Hook Form** - 表单管理
- **Zustand** - 状态管理

### 部署与监控
- **Vercel** - 应用部署
- **AWS S3** - 音频文件存储
- **Sentry** - 错误监控
- **Posthog** - 用户分析

## 核心功能

### 1. 文字转语音生成
- **单人语音**: 支持30种预建语音选项
- **多人语音**: 最多2个说话者的对话生成
- **语音控制**: 通过自然语言控制语调、语速、口音
- **多语言支持**: 24种语言自动检测和生成
- **音频格式**: 24kHz高质量WAV输出

### 2. 用户管理系统
- 邮箱注册/登录
- OAuth集成(Google, GitHub)
- 用户资料管理
- 使用量跟踪
- 订阅状态管理

### 3. 订阅与计费
- 多层级定价策略
- Stripe支付集成
- 使用量监控
- 自动计费
- 发票管理

### 4. 音频管理
- 生成历史记录
- 音频文件存储
- 批量下载
- 分享功能
- 音频播放器

## 定价策略

### 免费层 (Free)
- 每月1,000字符
- 5种基础语音
- 标准音质
- 带水印音频
- 7天历史保存

### 基础层 (Basic - $9.99/月)
- 每月50,000字符
- 30种语音选项
- 高音质输出
- 无水印
- 30天历史保存
- 邮件支持

### 专业层 (Pro - $29.99/月)
- 每月200,000字符
- 多人语音支持
- 批量处理
- API访问
- 1年历史保存
- 优先支持

### 企业层 (Enterprise - 定制)
- 无限字符使用
- 专属语音训练
- 白标解决方案
- SLA保证
- 专属客户经理

### 按需付费
- 超出配额: $0.001/字符
- 一次性包: 100,000字符 $19.99

## 数据库设计

### User表
```sql
id: UUID (主键)
email: String (唯一)
name: String
password: String (哈希)
subscriptionPlan: Enum (FREE, BASIC, PRO, ENTERPRISE)
subscriptionStatus: Enum (ACTIVE, CANCELLED, EXPIRED)
usageQuota: Integer (月度配额)
usedQuota: Integer (已使用)
stripeCustomerId: String
createdAt: DateTime
updatedAt: DateTime
```

### AudioGeneration表
```sql
id: UUID (主键)
userId: UUID (外键)
inputText: Text
outputAudioUrl: String
voiceName: String
language: String
style: String
duration: Float (秒)
characterCount: Integer
fileSize: Integer (字节)
createdAt: DateTime
```

### Subscription表
```sql
id: UUID (主键)
userId: UUID (外键)
planType: Enum
status: Enum
startDate: DateTime
endDate: DateTime
stripeSubscriptionId: String
amount: Decimal
currency: String
```

### Usage表
```sql
id: UUID (主键)
userId: UUID (外键)
date: Date
characterCount: Integer
audioCount: Integer
```

## API设计

### 认证路由
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/me` - 获取当前用户信息

### TTS路由
- `POST /api/tts/generate` - 生成语音
- `GET /api/tts/voices` - 获取可用语音列表
- `GET /api/tts/languages` - 获取支持语言列表
- `GET /api/tts/history` - 获取生成历史

### 用户路由
- `GET /api/user/profile` - 获取用户资料
- `PUT /api/user/profile` - 更新用户资料
- `GET /api/user/usage` - 获取使用统计
- `GET /api/user/subscription` - 获取订阅信息

### 支付路由
- `POST /api/payment/create-checkout` - 创建支付会话
- `POST /api/payment/webhook` - Stripe webhook
- `POST /api/payment/cancel-subscription` - 取消订阅

## 页面结构

### 公开页面
- `/` - 首页 (产品介绍、定价、演示)
- `/pricing` - 详细定价页面
- `/docs` - API文档
- `/about` - 关于我们
- `/contact` - 联系我们

### 认证页面
- `/auth/login` - 登录页面
- `/auth/register` - 注册页面
- `/auth/forgot-password` - 忘记密码

### 用户页面
- `/dashboard` - 用户仪表板
- `/generate` - 语音生成工具
- `/history` - 生成历史
- `/settings` - 用户设置
- `/subscription` - 订阅管理
- `/billing` - 账单管理

## 开发阶段

### 第一阶段: MVP (4-6周)
- [ ] T3应用基础搭建
- [ ] 用户认证系统
- [ ] 基础TTS功能(单人语音)
- [ ] 简单使用量跟踪
- [ ] 基础UI/UX设计
- [ ] 数据库设计和迁移

### 第二阶段: Beta (2-3周)
- [ ] 多人语音支持
- [ ] 语音风格控制
- [ ] 音频历史管理
- [ ] 基础订阅系统
- [ ] Stripe支付集成
- [ ] 用户仪表板

### 第三阶段: 正式版 (2-3周)
- [ ] 批量处理功能
- [ ] API访问功能
- [ ] 详细使用统计
- [ ] 客户支持系统
- [ ] 性能优化
- [ ] 安全加固

### 第四阶段: 扩展 (持续)
- [ ] 移动应用开发
- [ ] 更多语音选项
- [ ] 企业级功能
- [ ] 国际化支持
- [ ] 高级分析功能

## 安全考虑

### API安全
- Google Gemini API密钥安全存储
- 请求速率限制
- 输入验证和清理
- CORS配置

### 用户安全
- 密码强度要求
- 会话管理
- CSRF保护
- XSS防护

### 数据安全
- 数据库加密
- 敏感信息脱敏
- 定期备份
- 访问日志

## 性能优化

### 前端优化
- 代码分割
- 图片优化
- 缓存策略
- CDN使用

### 后端优化
- 数据库查询优化
- Redis缓存
- 异步处理
- 负载均衡

### 音频处理
- 流式传输
- 压缩算法
- CDN分发
- 缓存策略

## 监控与分析

### 应用监控
- Sentry错误追踪
- 性能指标监控
- API调用统计
- 用户行为分析

### 业务指标
- 用户注册转化率
- 订阅转化率
- 月活跃用户
- 收入分析
- 客户生命周期价值

## 风险评估

### 技术风险
- Google API限制和成本
- 第三方服务依赖
- 扩展性挑战
- 安全漏洞

### 商业风险
- 竞争对手威胁
- 用户获取成本
- 定价策略调整
- 法律合规要求

## 成功指标

### 技术指标
- 99.9%系统可用性
- <2秒页面加载时间
- <5秒音频生成时间
- 零安全事故

### 业务指标
- 月度经常性收入(MRR)增长
- 客户获取成本(CAC)
- 客户生命周期价值(LTV)
- 净推荐值(NPS)

## 竞争优势

1. **技术领先**: 基于最新Gemini 2.5 TTS技术
2. **功能丰富**: 强大的多人语音和风格控制
3. **开发者友好**: 完整的API和文档
4. **灵活定价**: 多层级订阅和按需付费
5. **用户体验**: 直观的界面和快速响应

## 下一步行动

1. 设置开发环境和T3项目
2. 配置Google Gemini API访问
3. 设计和实现数据库架构
4. 开发MVP核心功能
5. 集成支付系统
6. 部署测试环境
7. 用户测试和反馈收集
8. 正式发布和营销推广
