# KhmerSpeech - 高棉语AI文字转语音平台

## 项目概述

KhmerSpeech是一个专注于高棉语(柬埔寨语)的文字转语音(TTS)应用，使用T3技术栈构建。该平台集成OpenAI和Google Gemini两个AI语音生成API，为高棉语用户提供高质量的语音合成服务。作为第一个版本，我们专注于服务柬埔寨本地市场和海外高棉语社区。

## 技术栈

### 核心框架
- **Next.js 14** (App Router) - React全栈框架
- **TypeScript** - 类型安全的JavaScript
- **T3 Stack** - 现代化全栈开发工具链

### 数据层
- **Prisma** - 类型安全的ORM
- **PostgreSQL** - 主数据库
- **Redis** - 缓存和会话存储

### API层
- **tRPC** - 端到端类型安全的API
- **Google Gemini API** - 主要AI语音生成服务
- **OpenAI TTS API** - 备用AI语音生成服务
- **Stripe API** - 支付处理

### 认证与安全
- **NextAuth.js** - 身份认证
- **JWT** - 会话管理
- **bcrypt** - 密码加密

### 前端
- **Tailwind CSS** - 实用优先的CSS框架
- **Headless UI** - 无样式组件库
- **React Hook Form** - 表单管理
- **Zustand** - 状态管理

### 部署与监控
- **Vercel** - 应用部署
- **AWS S3** - 音频文件存储
- **Sentry** - 错误监控
- **Posthog** - 用户分析

## 核心功能

### 1. 高棉语文字转语音生成
- **双API支持**: Google Gemini API + OpenAI TTS API
- **高棉语专用**: 专门优化高棉语语音合成质量
- **自定义角色系统**:
  - 每个API都有独立的高棉语角色名称
  - 用户选择角色，系统自动使用对应的API
  - 用户不需要知道底层API技术细节
- **Gemini API角色**:
  - សុភា (Sopha) - 男性，沉稳声音
  - ចន្ទ្រា (Chandra) - 女性，清脆声音
  - រតន៍ (Roat) - 男性，成熟声音
- **OpenAI API角色**:
  - វិចិត្រ (Vichit) - 男性，温和声音
  - សុភាព (Sopheak) - 女性，优雅声音
  - កញ្ញា (Kanha) - 女性，活泼声音
- **音频格式**: 支持MP3、WAV、AAC等多种格式
- **语音控制**: 语速调节、音调控制
- **文本处理**: 高棉语文本预处理和优化

### 2. 用户管理系统
- 邮箱注册/登录
- OAuth集成(Google, GitHub)
- 用户资料管理
- 使用量跟踪
- 订阅状态管理

### 3. 订阅与计费
- 多层级定价策略
- Stripe支付集成
- 使用量监控
- 自动计费
- 发票管理

### 4. 音频管理
- 生成历史记录
- 音频文件存储
- 音频播放器
- 下载功能
- API选择记录

### 5. 角色语音系统
- **独立角色**: 每个API都有独立的高棉语角色
- **简单选择**: 用户直接选择喜欢的角色即可
- **API透明**: 用户不需要了解技术细节
- **角色预览**: 每个角色都有语音样本预览
- **使用统计**: 各角色使用情况和用户偏好统计
- **成本跟踪**: 不同API的使用成本分别统计

## 定价策略 (针对柬埔寨市场)

### 免费层 (Free)
- 每月500字符
- 2个基础角色(សុភា, វិចិត្រ - 各API一个)
- 标准音质(MP3)
- 7天历史保存
- 仅高棉语支持

### 基础层 ($2.99/月 或 12,000瑞尔/月)
- 每月10,000字符
- 4个语音角色(Gemini 2个 + OpenAI 2个)
- 高音质输出(WAV)
- 30天历史保存
- 角色预览功能
- 邮件支持

### 专业层 ($7.99/月 或 32,000瑞尔/月)
- 每月50,000字符
- 所有6个语音角色
- 批量处理
- API访问
- 90天历史保存
- 优先支持
- 角色使用统计

### 企业层 (定制)
- 无限字符使用
- 专属API配额
- 白标解决方案
- 本地化支持
- 专属客户经理

### 按需付费
- 超出配额: $0.0005/字符
- 一次性包: 20,000字符 $4.99

## 数据库设计

### User表
```sql
id: UUID (主键)
email: String (唯一)
name: String
password: String (哈希)
subscriptionPlan: Enum (FREE, BASIC, PRO, ENTERPRISE)
subscriptionStatus: Enum (ACTIVE, CANCELLED, EXPIRED)
usageQuota: Integer (月度配额)
usedQuota: Integer (已使用)
stripeCustomerId: String
createdAt: DateTime
updatedAt: DateTime
```

### VoiceCharacter表 (角色定义)
```sql
id: UUID (主键)
characterName: String (高棉语角色名)
characterNameEn: String (英文角色名)
apiProvider: Enum (GEMINI, OPENAI)
apiVoiceName: String (对应API的语音名)
gender: Enum (MALE, FEMALE)
description: String (角色描述)
previewAudioUrl: String (预览音频链接)
isActive: Boolean (是否启用)
tier: Enum (FREE, BASIC, PRO) (可用层级)
sortOrder: Integer (显示顺序)
createdAt: DateTime
```

### AudioGeneration表
```sql
id: UUID (主键)
userId: UUID (外键)
inputText: Text (高棉语文本)
outputAudioUrl: String
characterId: UUID (外键 - 角色ID)
apiProvider: Enum (GEMINI, OPENAI)
actualVoiceName: String (实际使用的API语音名)
audioFormat: Enum (MP3, WAV, AAC)
speed: Float (语速 0.25-4.0)
duration: Float (秒)
characterCount: Integer
fileSize: Integer (字节)
cost: Decimal (生成成本)
createdAt: DateTime
```

### Subscription表
```sql
id: UUID (主键)
userId: UUID (外键)
planType: Enum
status: Enum
startDate: DateTime
endDate: DateTime
stripeSubscriptionId: String
amount: Decimal
currency: String
```

### Usage表
```sql
id: UUID (主键)
userId: UUID (外键)
date: Date
characterCount: Integer
audioCount: Integer
```

## API设计

### 认证路由
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/me` - 获取当前用户信息

### TTS路由
- `POST /api/tts/generate` - 生成高棉语语音(使用角色ID)
- `GET /api/tts/characters` - 获取可用角色列表
- `GET /api/tts/characters/:id` - 获取特定角色详情
- `GET /api/tts/history` - 获取生成历史
- `POST /api/tts/preview` - 角色语音预览

### 用户路由
- `GET /api/user/profile` - 获取用户资料
- `PUT /api/user/profile` - 更新用户资料
- `GET /api/user/usage` - 获取使用统计
- `GET /api/user/subscription` - 获取订阅信息

### 支付路由
- `POST /api/payment/create-checkout` - 创建支付会话
- `POST /api/payment/webhook` - Stripe webhook
- `POST /api/payment/cancel-subscription` - 取消订阅

## 页面结构

### 公开页面
- `/` - 首页 (高棉语TTS介绍、定价、演示)
- `/pricing` - 详细定价页面
- `/docs` - API文档
- `/about` - 关于我们 (支持高棉语社区)
- `/contact` - 联系我们
- `/demo` - 在线演示页面

### 认证页面
- `/auth/login` - 登录页面
- `/auth/register` - 注册页面
- `/auth/forgot-password` - 忘记密码

### 用户页面
- `/dashboard` - 用户仪表板
- `/generate` - 高棉语语音生成工具
- `/history` - 生成历史
- `/settings` - 用户设置
- `/subscription` - 订阅管理
- `/billing` - 账单管理
- `/compare` - API效果对比页面

## 开发阶段

### 第一阶段: MVP (3-4周)
- [ ] T3应用基础搭建
- [ ] 用户认证系统
- [ ] Google Gemini API集成(主要)
- [ ] 角色映射系统设计
- [ ] 高棉语文本处理
- [ ] 基础语音生成功能
- [ ] 简单使用量跟踪
- [ ] 基础UI/UX设计(支持高棉语)
- [ ] 数据库设计和迁移

### 第二阶段: 双API支持 (2-3周)
- [ ] OpenAI TTS API集成
- [ ] OpenAI角色定义和映射
- [ ] 角色预览音频生成
- [ ] 音频历史管理
- [ ] 使用统计和成本跟踪
- [ ] 用户仪表板完善

### 第三阶段: 商业化 (2-3周)
- [ ] Stripe支付集成
- [ ] 订阅系统
- [ ] 瑞尔(KHR)本地货币支持
- [ ] 详细使用统计和角色偏好分析
- [ ] 客户支持系统
- [ ] 性能优化

### 第四阶段: 本地化扩展 (持续)
- [ ] 高棉语界面完全本地化
- [ ] 柬埔寨本地支付方式
- [ ] 移动应用开发
- [ ] 社区功能
- [ ] 教育机构合作

## 安全考虑

### API安全
- Gemini和OpenAI API密钥安全存储
- 分别的API请求速率限制
- 高棉语文本输入验证和清理
- 角色数据保护
- CORS配置
- 各API使用量监控和告警

### 用户安全
- 密码强度要求
- 会话管理
- CSRF保护
- XSS防护

### 数据安全
- 数据库加密
- 敏感信息脱敏
- 定期备份
- 访问日志

## 性能优化

### 前端优化
- 代码分割
- 图片优化
- 缓存策略
- CDN使用

### 后端优化
- 数据库查询优化
- Redis缓存
- 异步处理
- 负载均衡

### 音频处理
- 多格式支持(MP3, WAV, AAC)
- 音频质量优化
- CDN分发
- 缓存策略
- 高棉语语音质量评估

## 监控与分析

### 应用监控
- Sentry错误追踪
- 性能指标监控
- API调用统计
- 用户行为分析

### 业务指标
- 用户注册转化率
- 订阅转化率
- 月活跃用户
- 收入分析
- 客户生命周期价值

## 风险评估

### 技术风险
- Gemini和OpenAI API限制和成本
- 双API依赖风险
- 角色质量一致性
- 高棉语处理质量
- 扩展性挑战
- 安全漏洞

### 商业风险
- 柬埔寨市场接受度
- 本地化竞争对手
- 用户获取成本
- 汇率波动影响
- 法律合规要求

## 成功指标

### 技术指标
- 99.9%系统可用性
- <2秒页面加载时间
- <5秒音频生成时间
- 零安全事故

### 业务指标
- 月度经常性收入(MRR)增长
- 客户获取成本(CAC)
- 客户生命周期价值(LTV)
- 净推荐值(NPS)

## 竞争优势

1. **高棉语专业化**: 专门针对高棉语优化的TTS服务
2. **双API选择**: 提供Gemini和OpenAI两套独立角色选择
3. **本地化角色**: 每个角色都有高棉语名称和文化背景
4. **简单易用**: 用户只需选择角色，无需了解技术细节
5. **本地化定价**: 适合柬埔寨市场的定价策略
6. **社区导向**: 专注服务高棉语社区需求
7. **技术先进**: 集成最新的AI语音技术

## 下一步行动

1. 设置开发环境和T3项目
2. 申请Google Gemini和OpenAI API访问权限
3. 研究高棉语TTS最佳实践
4. 设计角色映射系统
5. 设计和实现数据库架构
6. 开发MVP核心功能(Gemini角色优先)
7. 添加OpenAI API支持和角色定义
8. 实现角色预览和选择功能
9. 集成支付系统(支持美元和瑞尔)
10. 部署测试环境
11. 柬埔寨本地用户测试
12. 正式发布和本地化营销

## 角色系统设计

### Gemini API角色定义
```javascript
const geminiCharacters = [
  {
    id: "sopha_gemini",
    name: "សុភា", // Sopha
    nameEn: "Sopha",
    apiProvider: "GEMINI",
    apiVoiceName: "Kore",
    gender: "MALE",
    description: "ប្រុសវ័យកណ្តាល សំឡេងស្ងប់ស្ងាត់", // 中年男性，声音沉稳
    tier: "FREE"
  },
  {
    id: "chandra_gemini",
    name: "ចន្ទ្រា", // Chandra
    nameEn: "Chandra",
    apiProvider: "GEMINI",
    apiVoiceName: "Puck",
    gender: "FEMALE",
    description: "ស្រីវ័យក្មេង សំឡេងស្រស់ស្អាត", // 年轻女性，声音清脆
    tier: "BASIC"
  },
  {
    id: "roat_gemini",
    name: "រតន៍", // Roat
    nameEn: "Roat",
    apiProvider: "GEMINI",
    apiVoiceName: "Charon",
    gender: "MALE",
    description: "ប្រុសវ័យចាស់ សំឡេងមានបទពិសោធន៍", // 年长男性，声音有经验
    tier: "PRO"
  }
];
```

### OpenAI API角色定义
```javascript
const openaiCharacters = [
  {
    id: "vichit_openai",
    name: "វិចិត្រ", // Vichit
    nameEn: "Vichit",
    apiProvider: "OPENAI",
    apiVoiceName: "onyx",
    gender: "MALE",
    description: "ប្រុសវ័យក្មេង សំឡេងទន់ភ្លន់", // 年轻男性，声音温和
    tier: "FREE"
  },
  {
    id: "sopheak_openai",
    name: "សុភាព", // Sopheak
    nameEn: "Sopheak",
    apiProvider: "OPENAI",
    apiVoiceName: "nova",
    gender: "FEMALE",
    description: "ស្រីវ័យកណ្តាល សំឡេងឆ្លាតវៃ", // 中年女性，声音优雅
    tier: "BASIC"
  },
  {
    id: "kanha_openai",
    name: "កញ្ញា", // Kanha
    nameEn: "Kanha",
    apiProvider: "OPENAI",
    apiVoiceName: "shimmer",
    gender: "FEMALE",
    description: "ស្រីវ័យក្មេង សំឡេងរីករាយ", // 年轻女性，声音活泼
    tier: "PRO"
  }
];
```

### 用户体验
- 用户看到所有可用角色列表
- 每个角色都有独特的高棉语名称和描述
- 用户不需要知道底层使用哪个API
- 角色预览功能让用户试听
- 根据订阅层级显示可用角色

## 特殊考虑

### 高棉语处理
- 高棉语Unicode支持
- 文本预处理和清理
- 语音质量评估标准
- 本地化用户界面

### 柬埔寨市场
- 本地支付方式集成
- 瑞尔货币支持
- 本地化客户服务
- 教育和政府机构合作机会
